#!/usr/bin/env node

/**
 * Vendor Dependencies Update Script
 * 
 * This script automatically updates local vendor files from npm packages.
 * It copies the necessary files from node_modules to the scripts/vendor directory.
 * 
 * Usage:
 * - npm run update-vendors (manual update)
 * - Runs automatically after npm install via postinstall hook
 */

const fs = require('fs');
const path = require('path');

// Configuration for vendor dependencies
const VENDOR_CONFIG = {
    leaflet: {
        source: 'node_modules/leaflet/dist',
        destination: 'scripts/vendor/leaflet',
        files: [
            'leaflet.js',
            'leaflet-src.js',
            'leaflet-src.esm.js',
            'leaflet.css'
        ],
        directories: [
            'images'
        ]
    },
    // Note: Rive files are kept as-is since they're custom builds
    // The @rive-app/canvas-lite package doesn't contain the same files
};

/**
 * Ensures a directory exists, creating it if necessary
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
}

/**
 * Copies a file from source to destination
 */
function copyFile(src, dest) {
    try {
        if (fs.existsSync(src)) {
            fs.copyFileSync(src, dest);
            console.log(`✓ Copied: ${src} → ${dest}`);
            return true;
        } else {
            console.warn(`⚠ Source file not found: ${src}`);
            return false;
        }
    } catch (error) {
        console.error(`✗ Failed to copy ${src}: ${error.message}`);
        return false;
    }
}

/**
 * Recursively copies a directory
 */
function copyDirectory(src, dest) {
    try {
        if (!fs.existsSync(src)) {
            console.warn(`⚠ Source directory not found: ${src}`);
            return false;
        }

        ensureDirectoryExists(dest);
        
        const items = fs.readdirSync(src);
        let success = true;

        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                success = copyDirectory(srcPath, destPath) && success;
            } else {
                success = copyFile(srcPath, destPath) && success;
            }
        }

        return success;
    } catch (error) {
        console.error(`✗ Failed to copy directory ${src}: ${error.message}`);
        return false;
    }
}

/**
 * Updates vendor files for a specific package
 */
function updateVendorPackage(packageName, config) {
    console.log(`\n📦 Updating ${packageName}...`);
    
    const { source, destination, files = [], directories = [] } = config;
    
    // Ensure destination directory exists
    ensureDirectoryExists(destination);
    
    let allSuccess = true;
    
    // Copy individual files
    for (const file of files) {
        const srcPath = path.join(source, file);
        const destPath = path.join(destination, file);
        allSuccess = copyFile(srcPath, destPath) && allSuccess;
    }
    
    // Copy directories
    for (const dir of directories) {
        const srcPath = path.join(source, dir);
        const destPath = path.join(destination, dir);
        allSuccess = copyDirectory(srcPath, destPath) && allSuccess;
    }
    
    if (allSuccess) {
        console.log(`✅ ${packageName} updated successfully`);
    } else {
        console.log(`⚠ ${packageName} updated with some warnings`);
    }
    
    return allSuccess;
}

/**
 * Main function to update all vendor dependencies
 */
function updateAllVendors() {
    console.log('🔄 Starting vendor dependencies update...\n');
    
    let overallSuccess = true;
    
    // Check if node_modules exists
    if (!fs.existsSync('node_modules')) {
        console.error('❌ node_modules directory not found. Please run "npm install" first.');
        process.exit(1);
    }
    
    // Update each configured vendor package
    for (const [packageName, config] of Object.entries(VENDOR_CONFIG)) {
        try {
            const success = updateVendorPackage(packageName, config);
            overallSuccess = overallSuccess && success;
        } catch (error) {
            console.error(`❌ Failed to update ${packageName}: ${error.message}`);
            overallSuccess = false;
        }
    }
    
    console.log('\n' + '='.repeat(50));
    if (overallSuccess) {
        console.log('✅ All vendor dependencies updated successfully!');
    } else {
        console.log('⚠ Vendor dependencies updated with some issues. Check the log above.');
    }
    console.log('='.repeat(50));
}

/**
 * Display help information
 */
function showHelp() {
    console.log(`
Vendor Dependencies Update Script

Usage:
  node scripts/update-vendors.js [options]

Options:
  --help, -h    Show this help message
  --list, -l    List configured vendor packages

This script copies vendor dependencies from node_modules to scripts/vendor/
It runs automatically after 'npm install' via the postinstall hook.
`);
}

/**
 * List configured vendor packages
 */
function listVendorPackages() {
    console.log('📋 Configured vendor packages:\n');
    
    for (const [packageName, config] of Object.entries(VENDOR_CONFIG)) {
        console.log(`📦 ${packageName}`);
        console.log(`   Source: ${config.source}`);
        console.log(`   Destination: ${config.destination}`);
        console.log(`   Files: ${config.files?.join(', ') || 'none'}`);
        console.log(`   Directories: ${config.directories?.join(', ') || 'none'}`);
        console.log('');
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

if (args.includes('--list') || args.includes('-l')) {
    listVendorPackages();
    process.exit(0);
}

// Run the main update function
if (require.main === module) {
    updateAllVendors();
}

module.exports = {
    updateAllVendors,
    updateVendorPackage,
    VENDOR_CONFIG
};
