{"name": "debounce", "version": "2.2.0", "description": "Delay function calls until a set time elapses after the last invocation", "license": "MIT", "repository": "sindresorhus/debounce", "funding": "https://github.com/sponsors/sindresorhus", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "main": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && node --test"}, "files": ["index.js", "index.d.ts"], "keywords": ["debounce", "debouncing", "function", "throttle", "invoke", "limit", "limited", "interval", "rate", "batch", "ratelimit"], "devDependencies": {"sinon": "^17.0.1", "xo": "^0.56.0"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}}