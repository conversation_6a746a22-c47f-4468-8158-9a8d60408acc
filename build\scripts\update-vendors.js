#!/usr/bin/env node

/**
 * Vendor Dependencies Update Script
 * 
 * This script automatically updates local vendor files from npm packages.
 * It copies the necessary files from node_modules to the scripts/vendor directory.
 * 
 * Usage:
 * - npm run update-vendors (manual update)
 * - Runs automatically after npm install via postinstall hook
 */

const fs = require('fs');
const path = require('path');

// Configuration for vendor dependencies
const VENDOR_CONFIG = {
    leaflet: {
        source: 'node_modules/leaflet/dist',
        destination: 'scripts/vendor/leaflet',
        files: [
            'leaflet.js',
            'leaflet-src.js',
            'leaflet-src.esm.js',
            'leaflet.css'
        ],
        directories: [
            'images'
        ]
    },
    rive: {
        source: 'node_modules/@rive-app/canvas-lite',
        destination: 'scripts/vendor/rive',
        files: [
            'rive.js',
            'rive.wasm',
            'rive_fallback.wasm'
        ],
        updateMode: 'optional', // Only update if user explicitly requests it
        backupOriginal: true     // Backup existing files before updating
    }
};

/**
 * Ensures a directory exists, creating it if necessary
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`Created directory: ${dirPath}`);
    }
}

/**
 * Copies a file from source to destination
 */
function copyFile(src, dest) {
    try {
        if (fs.existsSync(src)) {
            fs.copyFileSync(src, dest);
            console.log(`✓ Copied: ${src} → ${dest}`);
            return true;
        } else {
            console.warn(`⚠ Source file not found: ${src}`);
            return false;
        }
    } catch (error) {
        console.error(`✗ Failed to copy ${src}: ${error.message}`);
        return false;
    }
}

/**
 * Recursively copies a directory
 */
function copyDirectory(src, dest) {
    try {
        if (!fs.existsSync(src)) {
            console.warn(`⚠ Source directory not found: ${src}`);
            return false;
        }

        ensureDirectoryExists(dest);
        
        const items = fs.readdirSync(src);
        let success = true;

        for (const item of items) {
            const srcPath = path.join(src, item);
            const destPath = path.join(dest, item);
            
            if (fs.statSync(srcPath).isDirectory()) {
                success = copyDirectory(srcPath, destPath) && success;
            } else {
                success = copyFile(srcPath, destPath) && success;
            }
        }

        return success;
    } catch (error) {
        console.error(`✗ Failed to copy directory ${src}: ${error.message}`);
        return false;
    }
}

/**
 * Creates a backup of existing files
 */
function backupFiles(destination, files) {
    const backupDir = `${destination}.backup.${Date.now()}`;
    let backedUp = false;

    for (const file of files) {
        const filePath = path.join(destination, file);
        if (fs.existsSync(filePath)) {
            if (!backedUp) {
                ensureDirectoryExists(backupDir);
                console.log(`📋 Creating backup: ${backupDir}`);
                backedUp = true;
            }
            const backupPath = path.join(backupDir, file);
            copyFile(filePath, backupPath);
        }
    }

    return backedUp ? backupDir : null;
}

/**
 * Updates vendor files for a specific package
 */
function updateVendorPackage(packageName, config, forceUpdate = false) {
    const { source, destination, files = [], directories = [], updateMode, backupOriginal } = config;

    // Handle optional updates (like Rive)
    if (updateMode === 'optional' && !forceUpdate) {
        console.log(`\n⏭️  Skipping ${packageName} (optional update - use --force-rive to update)`);
        return true;
    }

    console.log(`\n📦 Updating ${packageName}...`);

    // Ensure destination directory exists
    ensureDirectoryExists(destination);

    // Create backup if requested
    let backupPath = null;
    if (backupOriginal && (files.length > 0 || directories.length > 0)) {
        backupPath = backupFiles(destination, files);
        if (backupPath) {
            console.log(`✓ Backup created: ${backupPath}`);
        }
    }

    let allSuccess = true;

    // Copy individual files
    for (const file of files) {
        const srcPath = path.join(source, file);
        const destPath = path.join(destination, file);
        allSuccess = copyFile(srcPath, destPath) && allSuccess;
    }

    // Copy directories
    for (const dir of directories) {
        const srcPath = path.join(source, dir);
        const destPath = path.join(destination, dir);
        allSuccess = copyDirectory(srcPath, destPath) && allSuccess;
    }

    if (allSuccess) {
        console.log(`✅ ${packageName} updated successfully`);
        if (backupPath) {
            console.log(`💡 Original files backed up to: ${backupPath}`);
        }
    } else {
        console.log(`⚠ ${packageName} updated with some warnings`);
    }

    return allSuccess;
}

/**
 * Main function to update all vendor dependencies
 */
function updateAllVendors(options = {}) {
    console.log('🔄 Starting vendor dependencies update...\n');

    const { forceRive = false } = options;
    let overallSuccess = true;

    // Check if node_modules exists
    if (!fs.existsSync('node_modules')) {
        console.error('❌ node_modules directory not found. Please run "npm install" first.');
        process.exit(1);
    }

    // Update each configured vendor package
    for (const [packageName, config] of Object.entries(VENDOR_CONFIG)) {
        try {
            const forceUpdate = packageName === 'rive' ? forceRive : false;
            const success = updateVendorPackage(packageName, config, forceUpdate);
            overallSuccess = overallSuccess && success;
        } catch (error) {
            console.error(`❌ Failed to update ${packageName}: ${error.message}`);
            overallSuccess = false;
        }
    }

    console.log('\n' + '='.repeat(50));
    if (overallSuccess) {
        console.log('✅ All vendor dependencies updated successfully!');
    } else {
        console.log('⚠ Vendor dependencies updated with some issues. Check the log above.');
    }

    if (!forceRive && VENDOR_CONFIG.rive) {
        console.log('\n💡 To update Rive files (may break compatibility):');
        console.log('   npm run update-vendors -- --force-rive');
    }

    console.log('='.repeat(50));
}

/**
 * Display help information
 */
function showHelp() {
    console.log(`
Vendor Dependencies Update Script

Usage:
  node scripts/update-vendors.js [options]

Options:
  --help, -h       Show this help message
  --list, -l       List configured vendor packages
  --force-rive     Force update Rive files (creates backup)

Examples:
  npm run update-vendors                    # Update all (skip optional)
  npm run update-vendors -- --force-rive   # Update all including Rive
  npm run update-vendors -- --list         # List configured packages

This script copies vendor dependencies from node_modules to scripts/vendor/
It runs automatically after 'npm install' via the postinstall hook.

⚠️  Rive files are marked as optional updates because they may contain
    custom modifications. Use --force-rive to update them.
`);
}

/**
 * List configured vendor packages
 */
function listVendorPackages() {
    console.log('📋 Configured vendor packages:\n');
    
    for (const [packageName, config] of Object.entries(VENDOR_CONFIG)) {
        console.log(`📦 ${packageName}`);
        console.log(`   Source: ${config.source}`);
        console.log(`   Destination: ${config.destination}`);
        console.log(`   Files: ${config.files?.join(', ') || 'none'}`);
        console.log(`   Directories: ${config.directories?.join(', ') || 'none'}`);
        console.log('');
    }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    showHelp();
    process.exit(0);
}

if (args.includes('--list') || args.includes('-l')) {
    listVendorPackages();
    process.exit(0);
}

// Run the main update function
if (require.main === module) {
    const options = {
        forceRive: args.includes('--force-rive')
    };
    updateAllVendors(options);
}

module.exports = {
    updateAllVendors,
    updateVendorPackage,
    VENDOR_CONFIG
};
