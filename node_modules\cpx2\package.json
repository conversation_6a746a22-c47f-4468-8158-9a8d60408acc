{"name": "cpx2", "version": "7.0.2", "description": "Copy file globs, watching for changes.", "engines": {"node": ">=18", "npm": ">=10"}, "main": "lib/index.js", "bin": {"cpx": "bin/index.js"}, "scripts": {"prepublishOnly": "git push --follow-tags && gh-release -y", "version": "auto-changelog -p --template keepachangelog auto-changelog --breaking-pattern 'BREAKING CHANGE:' && git add CHANGELOG.md", "_mocha": "_mocha \"test/*.js\" --timeout 35000", "clean": "rm -rf .nyc_output coverage test-ws", "coverage": "nyc report -r lcov && opener coverage/lcov-report/index.html", "lint": "eslint", "test": "nyc --require @babel/register npm run -s _mocha", "watch": "npm run -s _mocha -- --require @babel/register --watch --growl"}, "dependencies": {"debounce": "^2.0.0", "debug": "^4.1.1", "duplexer": "^0.1.1", "fs-extra": "^11.1.0", "glob": "^11.0.0", "glob2base": "0.0.12", "ignore": "^5.2.4", "minimatch": "^10.0.1", "p-map": "^6.0.0", "resolve": "^1.12.0", "safe-buffer": "^5.2.0", "shell-quote": "^1.8.0", "subarg": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.5.5", "@babel/register": "^7.5.5", "auto-changelog": "^2.2.0", "babel-preset-power-assert": "^3.0.0", "eslint": "^9.11.1", "eslint-plugin-mocha": "^10.5.0", "gh-release": "^7.0.2", "mocha": "^10.2.0", "neostandard": "^0.11.6", "nyc": "17.0.0", "opener": "^1.5.1", "p-event": "^6.0.0", "power-assert": "^1.6.1", "shelljs": "^0.8.3", "through": "^2.3.8"}, "repository": {"type": "git", "url": "https://github.com/bcomnes/cpx2.git"}, "keywords": ["cp", "cli", "tool", "commandline", "sync", "rsync", "watch", "observe", "copy", "dir", "directory", "directories", "file", "files"], "author": "<PERSON><PERSON>", "contributors": ["<PERSON><PERSON> Comnes <<EMAIL>> (https://bret.io)"], "license": "MIT", "bugs": {"url": "https://github.com/bcomnes/cpx2/issues"}, "homepage": "https://github.com/bcomnes/cpx2"}