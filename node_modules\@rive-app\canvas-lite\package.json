{"name": "@rive-app/canvas-lite", "version": "2.27.1", "description": "A lite version of Rive's canvas based web api.", "main": "rive.js", "homepage": "https://rive.app", "repository": {"type": "git", "url": "https://github.com/rive-app/rive-wasm/tree/master/js"}, "keywords": ["rive", "animation"], "author": "Rive", "contributors": ["<PERSON> <<EMAIL>> (https://rive.app)", "<PERSON> <<EMAIL>> (https://rive.app)", "<PERSON> <<EMAIL>> (https://rive.app)", "<PERSON><PERSON> <<EMAIL>> (https://rive.app)", "<PERSON> <<EMAIL>> (mailto:<EMAIL>)"], "license": "MIT", "files": ["rive.js", "rive.js.map", "rive.wasm", "rive_fallback.wasm", "rive.d.ts", "rive_advanced.mjs.d.ts"], "typings": "rive.d.ts", "dependencies": {}, "browser": {"fs": false, "path": false}}