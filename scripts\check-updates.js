#!/usr/bin/env node

/**
 * Dependency Update Checker
 * 
 * This script checks for available updates to your dependencies and provides
 * information about outdated packages.
 * 
 * Usage:
 * - npm run check-updates
 * - node scripts/check-updates.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Colors for console output
 */
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

/**
 * Colorize text for console output
 */
function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

/**
 * Execute a command and return the output
 */
function executeCommand(command, options = {}) {
    try {
        return execSync(command, { 
            encoding: 'utf8', 
            stdio: 'pipe',
            ...options 
        }).trim();
    } catch (error) {
        if (options.ignoreErrors) {
            return null;
        }
        throw error;
    }
}

/**
 * Check if npm-check-updates is available
 */
function checkNcuAvailable() {
    try {
        executeCommand('npx ncu --version', { ignoreErrors: true });
        return true;
    } catch {
        return false;
    }
}

/**
 * Get outdated packages using npm outdated
 */
function getOutdatedPackages() {
    try {
        const output = executeCommand('npm outdated --json', { ignoreErrors: true });
        return output ? JSON.parse(output) : {};
    } catch (error) {
        console.warn(colorize('⚠ Could not check for outdated packages with npm outdated', 'yellow'));
        return {};
    }
}

/**
 * Get package information from package.json
 */
function getPackageInfo() {
    try {
        const packagePath = path.join(process.cwd(), 'package.json');
        const packageContent = fs.readFileSync(packagePath, 'utf8');
        return JSON.parse(packageContent);
    } catch (error) {
        console.error(colorize('❌ Could not read package.json', 'red'));
        process.exit(1);
    }
}

/**
 * Display outdated packages information
 */
function displayOutdatedPackages(outdated) {
    if (Object.keys(outdated).length === 0) {
        console.log(colorize('✅ All packages are up to date!', 'green'));
        return;
    }

    console.log(colorize('\n📦 Outdated Packages:', 'bright'));
    console.log('='.repeat(80));

    for (const [packageName, info] of Object.entries(outdated)) {
        const current = info.current || 'not installed';
        const wanted = info.wanted || 'unknown';
        const latest = info.latest || 'unknown';
        const type = info.type || 'dependency';

        console.log(`\n${colorize(packageName, 'cyan')} (${type})`);
        console.log(`  Current: ${colorize(current, 'red')}`);
        console.log(`  Wanted:  ${colorize(wanted, 'yellow')}`);
        console.log(`  Latest:  ${colorize(latest, 'green')}`);
    }
}

/**
 * Provide update recommendations
 */
function provideUpdateRecommendations(outdated, packageInfo) {
    if (Object.keys(outdated).length === 0) {
        return;
    }

    console.log(colorize('\n🔧 Update Recommendations:', 'bright'));
    console.log('='.repeat(80));

    const hasVendorDeps = Object.keys(outdated).some(pkg => 
        ['leaflet', '@rive-app/canvas-lite'].includes(pkg)
    );

    if (hasVendorDeps) {
        console.log(colorize('\n⚠ Vendor Dependencies Detected:', 'yellow'));
        console.log('Some outdated packages are used as vendor dependencies.');
        console.log('After updating, run: ' + colorize('npm run update-vendors', 'cyan'));
    }

    console.log(colorize('\n📋 Update Commands:', 'bright'));
    console.log('1. Update to wanted versions (respects semver ranges):');
    console.log('   ' + colorize('npm update', 'cyan'));
    
    console.log('\n2. Update to latest versions (may break compatibility):');
    if (checkNcuAvailable()) {
        console.log('   ' + colorize('npx ncu -u && npm install', 'cyan'));
    } else {
        console.log('   Install npm-check-updates first:');
        console.log('   ' + colorize('npm install -g npm-check-updates', 'cyan'));
        console.log('   ' + colorize('npx ncu -u && npm install', 'cyan'));
    }

    console.log('\n3. Update specific packages:');
    for (const packageName of Object.keys(outdated)) {
        console.log(`   ${colorize(`npm install ${packageName}@latest`, 'cyan')}`);
    }
}

/**
 * Check for security vulnerabilities
 */
function checkSecurityVulnerabilities() {
    console.log(colorize('\n🔒 Security Audit:', 'bright'));
    console.log('='.repeat(80));

    try {
        const auditOutput = executeCommand('npm audit --json', { ignoreErrors: true });
        if (auditOutput) {
            const audit = JSON.parse(auditOutput);
            const vulnerabilities = audit.metadata?.vulnerabilities;

            if (vulnerabilities) {
                const total = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);
                
                if (total === 0) {
                    console.log(colorize('✅ No security vulnerabilities found!', 'green'));
                } else {
                    console.log(colorize(`⚠ Found ${total} security vulnerabilities:`, 'yellow'));
                    for (const [severity, count] of Object.entries(vulnerabilities)) {
                        if (count > 0) {
                            const color = severity === 'critical' || severity === 'high' ? 'red' : 'yellow';
                            console.log(`  ${colorize(severity, color)}: ${count}`);
                        }
                    }
                    console.log('\nRun: ' + colorize('npm audit fix', 'cyan') + ' to fix automatically');
                }
            }
        }
    } catch (error) {
        console.warn(colorize('⚠ Could not run security audit', 'yellow'));
    }
}

/**
 * Display vendor-specific information
 */
function displayVendorInfo() {
    console.log(colorize('\n📁 Vendor Dependencies Info:', 'bright'));
    console.log('='.repeat(80));
    
    const vendorPath = path.join(process.cwd(), 'scripts', 'vendor');
    
    if (fs.existsSync(vendorPath)) {
        const vendors = fs.readdirSync(vendorPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);
        
        console.log('Current vendor packages:');
        vendors.forEach(vendor => {
            console.log(`  • ${colorize(vendor, 'cyan')}`);
        });
        
        console.log('\nTo update vendor files after npm updates:');
        console.log('  ' + colorize('npm run update-vendors', 'cyan'));
    } else {
        console.log(colorize('No vendor directory found', 'yellow'));
    }
}

/**
 * Main function
 */
function main() {
    console.log(colorize('🔍 Checking for dependency updates...', 'bright'));
    
    const packageInfo = getPackageInfo();
    const outdated = getOutdatedPackages();
    
    displayOutdatedPackages(outdated);
    provideUpdateRecommendations(outdated, packageInfo);
    checkSecurityVulnerabilities();
    displayVendorInfo();
    
    console.log(colorize('\n✨ Update check complete!', 'bright'));
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Dependency Update Checker

Usage:
  node scripts/check-updates.js [options]

Options:
  --help, -h    Show this help message

This script checks for outdated dependencies and provides update recommendations.
It also checks for security vulnerabilities and provides vendor-specific guidance.
`);
    process.exit(0);
}

// Run the main function
if (require.main === module) {
    main();
}

module.exports = {
    getOutdatedPackages,
    checkSecurityVulnerabilities,
    main
};
