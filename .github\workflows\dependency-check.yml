name: Dependency Security Check

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch: # Allow manual triggering

jobs:
  dependency-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Check for outdated packages
      run: npm run check-updates
      
    - name: Run security audit
      run: npm audit --audit-level moderate
      
    - name: Verify vendor files are up to date
      run: |
        npm run update-vendors
        if [ -n "$(git status --porcelain)" ]; then
          echo "❌ Vendor files are out of date!"
          git status
          exit 1
        else
          echo "✅ Vendor files are up to date"
        fi
        
    - name: Check for high/critical vulnerabilities
      run: |
        AUDIT_RESULT=$(npm audit --json --audit-level high 2>/dev/null || echo '{}')
        HIGH_VULNS=$(echo $AUDIT_RESULT | jq -r '.metadata.vulnerabilities.high // 0')
        CRITICAL_VULNS=$(echo $AUDIT_RESULT | jq -r '.metadata.vulnerabilities.critical // 0')
        
        if [ "$HIGH_VULNS" -gt 0 ] || [ "$CRITICAL_VULNS" -gt 0 ]; then
          echo "❌ Found high/critical vulnerabilities: High=$HIGH_VULNS, Critical=$CRITICAL_VULNS"
          npm audit
          exit 1
        else
          echo "✅ No high/critical vulnerabilities found"
        fi
