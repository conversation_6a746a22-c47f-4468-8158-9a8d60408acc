name: npm bump

on:
  workflow_dispatch:
    inputs:
      newversion:
        description: 'npm version {major,minor,patch}'
        required: true

env:
  node_version: lts/*
  FORCE_COLOR: 2

jobs:
  version_and_release:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        # fetch full history so things like auto-changelog work properly
        fetch-depth: 0
    - name: Use Node.js ${{ env.node_version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.node_version }}
        # setting a registry enables the NODE_AUTH_TOKEN env variable where we can set an npm token.  REQUIRED
        registry-url: 'https://registry.npmjs.org'
    - run: npm i
    - run: npm test
    - name: npm version && npm publish
      uses: bcomnes/npm-bump@v2
      with:
        git_email: <EMAIL>
        git_username: ${{ github.actor }}
        newversion: ${{ github.event.inputs.newversion }}
        github_token: ${{ secrets.GITHUB_TOKEN }} # built in actions token.  Passed tp gh-release if in use.
        npm_token: ${{ secrets.NPM_TOKEN }} # user set secret token generated at npm
