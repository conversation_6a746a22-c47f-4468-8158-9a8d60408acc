{"name": "glob2base", "description": "Extracts a base path from a node-glob instance", "version": "0.0.12", "homepage": "http://github.com/wearefractal/glob2base", "repository": {"type": "git", "url": "git://github.com/wearefractal/glob2base.git"}, "author": "Fractal <<EMAIL>> (http://wearefractal.com/)", "main": "./index.js", "files": ["index.js", "lib"], "dependencies": {"find-index": "^0.1.1"}, "devDependencies": {"coveralls": "^2.6.1", "istanbul": "^0.3.2", "jshint": "^2.4.1", "jshint-stylish": "^1.0.0", "mocha": "^2.0.0", "mocha-lcov-reporter": "^0.0.1", "glob": "^4.0.0", "should": "^4.0.0"}, "scripts": {"lint": "jshint index.js --reporter node_modules/jshint-stylish/stylish.js --exclude node_modules", "test": "npm run-script lint && mocha --reporter spec", "coveralls": "istanbul cover _mocha --report lcovonly -- -R spec && cat ./coverage/lcov.info | coveralls && rm -rf ./coverage"}, "engines": {"node": ">= 0.10"}, "licenses": [{"type": "MIT", "url": "http://github.com/wearefractal/glob2base/raw/master/LICENSE"}]}