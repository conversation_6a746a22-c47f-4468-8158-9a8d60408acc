/* Start map.css */

/** 
 * Map component styles for service area display
 * Applies to Leaflet map and custom overlays
 */

/* Map container */
#service-area-map,
.leaflet-map-container {
  width: 100%;
  height: 350px;
  min-height: 250px;
  max-width: 600px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.13), 0 1.5px 6px 0 rgba(0,0,0,0.09);
  overflow: hidden;
  background: #f8fafc;
  position: relative;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 700px) {
  #service-area-map,
  .leaflet-map-container {
    height: 220px;
    max-width: 98vw;
    border-radius: 12px;
  }
}

/* Hide Leaflet attribution */
.leaflet-control-attribution {
  display: none !important;
}

/* Polygon overlay styling (service area) - using site's teal colors */
.leaflet-interactive {
  stroke: hsl(184, 100%, 35%) !important; /* site's primary dark teal */
  stroke-width: 3 !important;
  fill: hsl(184, 100%, 50%) !important;   /* site's primary lighter teal */
  fill-opacity: 0.25 !important;
  filter: drop-shadow(0 2px 6px rgba(0, 179, 204, 0.15));
  transition: fill-opacity 0.2s;
}

.leaflet-interactive:hover {
  fill-opacity: 0.35 !important;
  cursor: pointer;
}

/* Custom marker styling (if used) - using site's teal colors */
.map-custom-marker {
  background: #fff;
  border: 2px solid hsl(184, 100%, 35%);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  box-shadow: 0 1px 4px rgba(0, 179, 204, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-custom-marker-inner {
  width: 10px;
  height: 10px;
  background: hsl(184, 100%, 35%);
  border-radius: 50%;
}

/* Optional: fade-in animation for map */
@keyframes mapFadeIn {
  from { opacity: 0; transform: scale(0.98);}
  to   { opacity: 1; transform: scale(1);}
}

#service-area-map,
.leaflet-map-container {
  animation: mapFadeIn 0.7s cubic-bezier(.4,1.4,.6,1) 0.1s both;
}

/* Ensure map tiles are crisp on retina/high-DPI */
.leaflet-tile {
  image-rendering: auto;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
}

/* Optional: subtle border for map - using site's teal color */
#service-area-map,
.leaflet-map-container {
  border: 3.5px solid hsl(184, 100%, 40%);
}

/* Hide scrollbars inside map */
#service-area-map::-webkit-scrollbar,
.leaflet-map-container::-webkit-scrollbar {
  display: none;
}

/* Prevent accidental text selection on map */
#service-area-map,
.leaflet-map-container,
#service-area-map * {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

/* Custom zoom controls - styled to match site theme */
.leaflet-control-zoom {
  display: flex !important;
  flex-direction: column;
  position: absolute !important;
  top: 16px;
  right: 16px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid hsl(184, 100%, 40%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 179, 204, 0.15);
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
  font-size: 1.6rem;
  font-weight: bold;
  color: hsl(184, 100%, 35%);
  background: rgba(255, 255, 255, 0.9);
  border: none;
  outline: none;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.leaflet-control-zoom-in:hover,
.leaflet-control-zoom-out:hover {
  background: hsl(184, 100%, 50%);
  color: white;
  transform: scale(1.05);
}

.leaflet-control-zoom-in:active,
.leaflet-control-zoom-out:active {
  background: hsl(184, 100%, 40%);
  transform: scale(0.95);
}

/* Prevent zoom controls from being hidden by other rules */
.leaflet-top,
.leaflet-right {
  z-index: 1100 !important;
}

/* Ensure zoom controls are always visible and properly positioned */
.leaflet-control-zoom a {
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Override any potential hiding styles */
.leaflet-control-zoom,
.leaflet-control-zoom a,
.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Mobile-specific zoom control adjustments */
@media (max-width: 700px) {
  .leaflet-control-zoom {
    top: 12px;
    right: 12px;
  }

  .leaflet-control-zoom-in,
  .leaflet-control-zoom-out {
    width: 40px;
    height: 40px;
    font-size: 1.4rem;
  }
}

/* End map.css */
