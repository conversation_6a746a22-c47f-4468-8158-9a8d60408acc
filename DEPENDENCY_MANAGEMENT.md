# Dependency Management Guide

This project now uses automated dependency management to keep your local vendor files (like <PERSON><PERSON> and Rive) up to date automatically.

## 🚀 Quick Start

### Check for Updates
```bash
npm run check-updates
```

### Update Dependencies
```bash
npm update                    # Update to wanted versions (safe)
npm install package@latest    # Update specific package to latest
```

### Update Vendor Files
```bash
npm run update-vendors        # Manually update vendor files
```

## 📦 How It Works

### Automatic Updates
- **After `npm install`**: Vendor files are automatically updated via the `postinstall` hook
- **Leaflet**: Now managed as an npm dependency, automatically copied to `scripts/vendor/leaflet/`
- **Rive**: Kept as custom files (since npm package differs from your custom build)

### Vendor Configuration
The `scripts/update-vendors.js` script manages:
- **Leaflet**: Copies from `node_modules/leaflet/dist/` to `scripts/vendor/leaflet/`
  - `leaflet.js`, `leaflet-src.js`, `leaflet-src.esm.js`
  - `leaflet.css`
  - `images/` directory with all icons

### Update Checking
The `scripts/check-updates.js` script provides:
- 📊 Outdated package detection
- 🔒 Security vulnerability scanning  
- 💡 Update recommendations
- 📁 Vendor-specific guidance

## 🛠️ Available Commands

| Command | Description |
|---------|-------------|
| `npm run check-updates` | Check for outdated packages and security issues |
| `npm run update-vendors` | Update vendor files from node_modules (skips Rive) |
| `npm run update-vendors -- --force-rive` | Update all vendor files including Rive |
| `npm run update-rive` | Interactive Rive update tool |
| `npm run update-rive -- --compare` | Compare current vs NPM Rive versions |
| `npm run update-rive -- --update --force` | Update Rive files (creates backup) |
| `npm update` | Update packages to wanted versions (respects semver) |
| `npm audit` | Check for security vulnerabilities |
| `npm audit fix` | Automatically fix security issues |

## 📋 Maintenance Workflow

### Weekly/Monthly Routine
1. **Check for updates**:
   ```bash
   npm run check-updates
   ```

2. **Update dependencies** (choose one):
   ```bash
   # Safe updates (recommended)
   npm update
   
   # Latest versions (may break compatibility)
   npx ncu -u && npm install
   ```

3. **Test your application** after updates

4. **Vendor files update automatically** via postinstall hook

### Managing Rive Updates

Rive files require special handling because they may contain customizations:

1. **Check what's different**:
   ```bash
   npm run update-rive -- --compare
   ```

2. **Update safely** (creates backup):
   ```bash
   npm run update-rive -- --update --force
   ```

3. **List backups**:
   ```bash
   npm run update-rive -- --list-backups
   ```

4. **Interactive mode**:
   ```bash
   npm run update-rive
   ```

### Adding New Vendor Dependencies

1. **Add to package.json**:
   ```bash
   npm install new-package
   ```

2. **Configure in `scripts/update-vendors.js`**:
   ```javascript
   const VENDOR_CONFIG = {
     'new-package': {
       source: 'node_modules/new-package/dist',
       destination: 'scripts/vendor/new-package',
       files: ['new-package.js', 'new-package.css'],
       directories: ['assets']
     }
   };
   ```

3. **Update vendor files**:
   ```bash
   npm run update-vendors
   ```

## 🔧 Configuration

### Vendor Config (`scripts/update-vendors.js`)
```javascript
const VENDOR_CONFIG = {
  leaflet: {
    source: 'node_modules/leaflet/dist',
    destination: 'scripts/vendor/leaflet',
    files: ['leaflet.js', 'leaflet-src.js', 'leaflet-src.esm.js', 'leaflet.css'],
    directories: ['images']
  }
};
```

### Package.json Scripts
```json
{
  "scripts": {
    "update-vendors": "node scripts/update-vendors.js",
    "check-updates": "node scripts/check-updates.js",
    "postinstall": "npm run update-vendors"
  }
}
```

## 🚨 Important Notes

### Rive Dependencies
- **@rive-app/canvas-lite**: NPM package for Rive (latest official version)
- **Local Rive files**: In `scripts/vendor/rive/` (may be customized/older version)
- **WASM files**: Your current WASM files are identical to NPM, but JS differs
- **Safe updates**: Use dedicated Rive update tools with backup/comparison features

### Breaking Changes
- Always test after major version updates
- Use `npm update` for safe updates (respects semver ranges)
- Use `npx ncu -u && npm install` for latest versions (may break compatibility)

### Security
- Run `npm audit` regularly to check for vulnerabilities
- Use `npm audit fix` to automatically fix issues
- The update checker includes security scanning

## 🔍 Troubleshooting

### Vendor Files Not Updating
```bash
# Manually run the update script
npm run update-vendors

# Check if source files exist
ls node_modules/leaflet/dist/
```

### Permission Issues
```bash
# On Windows, run as administrator if needed
# On Unix systems, check file permissions
chmod +x scripts/update-vendors.js
```

### Missing Dependencies
```bash
# Reinstall all dependencies
rm -rf node_modules package-lock.json
npm install
```

## 📈 Benefits

✅ **Automated**: Dependencies update automatically after `npm install`  
✅ **Secure**: Regular security vulnerability checking  
✅ **Consistent**: Same files across all environments  
✅ **Maintainable**: Clear configuration and documentation  
✅ **Flexible**: Easy to add new vendor dependencies  
✅ **Safe**: Respects your existing file structure  

## 🎯 Next Steps

1. **Set up a schedule**: Consider running `npm run check-updates` weekly
2. **CI/CD Integration**: Add update checks to your deployment pipeline
3. **Version Pinning**: Consider pinning critical dependencies to specific versions
4. **Backup Strategy**: Keep backups of working vendor files before major updates

---

*This system replaces manual dependency management while preserving your existing vendor file structure and build process.*
