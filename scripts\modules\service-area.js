/**
 * Service Area Map Module
 * Handles the initialization and management of the service area map using Leaflet
 */

// Map instance variable
let mapInstance = null;

/**
 * Initialize the service area map
 */
function initMap() {
    // Prevent multiple initializations
    if (mapInstance) {
        return;
    }

    // Wait for the container to be visible
    setTimeout(() => {
        try {
            const mapContainer = document.getElementById('service-area-map');
            if (!mapContainer) {
                console.error('Map container not found');
                return;
            }

            // Check if container is visible
            if (mapContainer.offsetWidth === 0 || mapContainer.offsetHeight === 0) {
                console.error('Map container is not visible');
                return;
            }

            // Initialize Leaflet map with zoom controls explicitly enabled
            mapInstance = L.map('service-area-map', {
                zoomControl: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                touchZoom: true,
                // Security: Restrict map bounds to prevent excessive resource usage
                maxBounds: [
                    [28.5, -83.0], // Southwest corner
                    [29.8, -81.5]  // Northeast corner
                ],
                minZoom: 8,
                maxZoom: 16
            }).setView([29.18650726275689, -82.25163157253273], 10);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            }).addTo(mapInstance);

            // Optional: Load GeoJSON if file exists
            fetch('assets/map.geojson')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('GeoJSON file not found');
                    }
                    return response.json();
                })
                .then(geojson => {
                    const geoLayer = L.geoJSON(geojson, {
                        style: {
                            color: 'hsl(184, 100%, 35%)',
                            fillColor: 'hsl(184, 100%, 50%)',
                            fillOpacity: 0.25
                        }
                    }).addTo(mapInstance);
                    mapInstance.fitBounds(geoLayer.getBounds());
                })
                .catch(error => {
                    console.log('GeoJSON not loaded:', error.message);
                    // Map will still work without GeoJSON
                });

        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }, 100); // Small delay to ensure container is rendered
}

/**
 * Clean up the map instance
 */
function cleanupMap() {
    if (mapInstance) {
        mapInstance.remove();
        mapInstance = null;
    }
}

/**
 * Initialize the service area module
 */
export function initServiceArea() {
    // Make functions available globally for main.js navigation
    window.initMap = initMap;
    window.cleanupMap = cleanupMap;

    // Return cleanup function for module management
    return {
        cleanup: () => {
            cleanupMap();
            // Clean up global references
            if (window.initMap) {
                delete window.initMap;
            }
            if (window.cleanupMap) {
                delete window.cleanupMap;
            }
        }
    };
}