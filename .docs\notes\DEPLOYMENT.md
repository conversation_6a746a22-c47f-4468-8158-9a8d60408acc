# Deployment Guide

## Quick Deployment Process

### 1. Build for Production
```bash
npm run build
```

### 2. Upload to Web Host
1. Navigate to the `build/` folder
2. Select all files and folders inside `build/`
3. Upload to your web host's public_html or www directory

### 3. Verify Deployment
- Check that your site loads correctly
- Verify .htaccess is working (HTTPS redirect, security headers)
- Test all pages (index.html, booking-form.html, booking.html)

## What Gets Uploaded

✅ **Production Files:**
- All HTML pages
- All CSS and JavaScript
- All images and assets
- .htaccess (security and caching rules)
- sitemap.xml (with updated dates)
- robots.txt

❌ **Development Files (Excluded):**
- package.json
- server.js
- build.js
- build-config.js
- node_modules/
- Any .old, .backup, .map files

## Automated Features

### Sitemap Updates
Every build automatically updates your sitemap.xml with:
- Current date in proper ISO format (YYYY-MM-DD)
- Professional lastmod formatting for SEO

### File Organization
- Clean build directory on each build
- Proper file structure maintained
- Only production-ready files included

## Build Output Structure
```
build/
├── assets/
│   ├── favicon/
│   ├── images/
│   └── map.geojson
├── scripts/
│   ├── modules/
│   └── vendor/
├── styles/
│   ├── components/
│   └── effects/
├── index.html
├── booking.html
├── booking-form.html
├── .htaccess
├── sitemap.xml
└── robots.txt
```

## Tips for Efficient Deployment

1. **Always build before uploading** - Never upload source files directly
2. **Check build output** - Review the build/ folder before uploading
3. **Backup your live site** - Before major updates
4. **Test locally first** - Use `npm start` to test changes before building

## Troubleshooting

### Build Fails
- Check that all source files exist
- Ensure Node.js is installed
- Run `npm install` if dependencies are missing

### Missing Files After Upload
- Verify .htaccess was uploaded (it's hidden by default)
- Check that all subdirectories were uploaded
- Ensure file permissions are correct on your host

### Sitemap Not Updating
- Check that sitemap.xml exists in your source directory
- Verify the URLs in build-config.js match your actual URLs
- Ensure the build process completed successfully
