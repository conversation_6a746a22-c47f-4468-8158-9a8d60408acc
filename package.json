{"dependencies": {"@rive-app/canvas-lite": "^2.27.1", "leaflet": "^1.9.4"}, "devDependencies": {"cpx2": "^7.0.1"}, "name": "mobile-massage-main", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "build": "node build.js", "build:clean": "node -e \"require('fs').rmSync('build', {recursive: true, force: true}); console.log('Build directory cleaned')\"", "prebuild": "npm run build:clean", "update-vendors": "node scripts/update-vendors.js", "update-rive": "node scripts/update-rive.js", "check-updates": "node scripts/check-updates.js", "postinstall": "npm run update-vendors"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}