#!/usr/bin/env node

/**
 * Rive Update Script
 * 
 * This script specifically handles updating Rive files from the npm package.
 * It provides options to compare versions, create backups, and safely update.
 * 
 * Usage:
 * - npm run update-rive
 * - node scripts/update-rive.js [options]
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Paths
const VENDOR_RIVE_PATH = 'scripts/vendor/rive';
const NPM_RIVE_PATH = 'node_modules/@rive-app/canvas-lite';
const BACKUP_BASE_PATH = 'scripts/vendor/rive.backups';

// Files to manage
const RIVE_FILES = ['rive.js', 'rive.wasm', 'rive_fallback.wasm'];

/**
 * Colors for console output
 */
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

/**
 * Calculate file hash for comparison
 */
function getFileHash(filePath) {
    if (!fs.existsSync(filePath)) return null;
    const content = fs.readFileSync(filePath);
    return crypto.createHash('md5').update(content).digest('hex');
}

/**
 * Get file size in a readable format
 */
function getFileSize(filePath) {
    if (!fs.existsSync(filePath)) return 'N/A';
    const stats = fs.statSync(filePath);
    const bytes = stats.size;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
}

/**
 * Compare current and npm versions
 */
function compareVersions() {
    console.log(colorize('🔍 Comparing Rive versions...', 'bright'));
    console.log('='.repeat(80));

    if (!fs.existsSync(NPM_RIVE_PATH)) {
        console.log(colorize('❌ NPM Rive package not found. Run: npm install', 'red'));
        return false;
    }

    if (!fs.existsSync(VENDOR_RIVE_PATH)) {
        console.log(colorize('❌ Vendor Rive directory not found', 'red'));
        return false;
    }

    let hasChanges = false;

    for (const file of RIVE_FILES) {
        const vendorPath = path.join(VENDOR_RIVE_PATH, file);
        const npmPath = path.join(NPM_RIVE_PATH, file);

        console.log(`\n📄 ${colorize(file, 'cyan')}`);
        
        const vendorHash = getFileHash(vendorPath);
        const npmHash = getFileHash(npmPath);
        const vendorSize = getFileSize(vendorPath);
        const npmSize = getFileSize(npmPath);

        console.log(`  Current: ${vendorHash ? vendorHash.substring(0, 8) : 'missing'} (${vendorSize})`);
        console.log(`  NPM:     ${npmHash ? npmHash.substring(0, 8) : 'missing'} (${npmSize})`);

        if (vendorHash !== npmHash) {
            console.log(`  Status:  ${colorize('DIFFERENT', 'yellow')}`);
            hasChanges = true;
        } else {
            console.log(`  Status:  ${colorize('SAME', 'green')}`);
        }
    }

    console.log('\n' + '='.repeat(80));
    if (hasChanges) {
        console.log(colorize('📋 Summary: Files differ between current and NPM versions', 'yellow'));
    } else {
        console.log(colorize('✅ Summary: All files are identical', 'green'));
    }

    return hasChanges;
}

/**
 * Create a backup of current files
 */
function createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                     new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
    const backupPath = path.join(BACKUP_BASE_PATH, timestamp);

    if (!fs.existsSync(BACKUP_BASE_PATH)) {
        fs.mkdirSync(BACKUP_BASE_PATH, { recursive: true });
    }

    fs.mkdirSync(backupPath, { recursive: true });

    console.log(colorize(`📋 Creating backup: ${backupPath}`, 'blue'));

    for (const file of RIVE_FILES) {
        const sourcePath = path.join(VENDOR_RIVE_PATH, file);
        const backupFilePath = path.join(backupPath, file);

        if (fs.existsSync(sourcePath)) {
            fs.copyFileSync(sourcePath, backupFilePath);
            console.log(`  ✓ Backed up: ${file}`);
        }
    }

    return backupPath;
}

/**
 * Update Rive files from NPM
 */
function updateRiveFiles() {
    console.log(colorize('🔄 Updating Rive files from NPM package...', 'bright'));

    for (const file of RIVE_FILES) {
        const sourcePath = path.join(NPM_RIVE_PATH, file);
        const destPath = path.join(VENDOR_RIVE_PATH, file);

        if (fs.existsSync(sourcePath)) {
            fs.copyFileSync(sourcePath, destPath);
            console.log(`  ✓ Updated: ${file}`);
        } else {
            console.log(`  ⚠ NPM file not found: ${file}`);
        }
    }

    console.log(colorize('✅ Rive files updated successfully!', 'green'));
}

/**
 * List available backups
 */
function listBackups() {
    console.log(colorize('📋 Available Rive backups:', 'bright'));
    console.log('='.repeat(80));

    if (!fs.existsSync(BACKUP_BASE_PATH)) {
        console.log(colorize('No backups found', 'yellow'));
        return;
    }

    const backups = fs.readdirSync(BACKUP_BASE_PATH, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name)
        .sort()
        .reverse();

    if (backups.length === 0) {
        console.log(colorize('No backups found', 'yellow'));
        return;
    }

    backups.forEach((backup, index) => {
        const backupPath = path.join(BACKUP_BASE_PATH, backup);
        const files = fs.readdirSync(backupPath);
        console.log(`${index + 1}. ${colorize(backup, 'cyan')} (${files.length} files)`);
        console.log(`   Path: ${backupPath}`);
    });

    console.log(`\nTo restore a backup, copy files manually from the backup directory.`);
}

/**
 * Show help information
 */
function showHelp() {
    console.log(`
${colorize('Rive Update Script', 'bright')}

Usage:
  node scripts/update-rive.js [options]

Options:
  --help, -h        Show this help message
  --compare, -c     Compare current vs NPM versions
  --update, -u      Update Rive files from NPM (creates backup)
  --list-backups    List available backups
  --force           Skip confirmation prompts

Examples:
  npm run update-rive                    # Interactive mode
  npm run update-rive -- --compare      # Just compare versions
  npm run update-rive -- --update       # Update with confirmation
  npm run update-rive -- --update --force  # Update without confirmation

${colorize('⚠️  Important Notes:', 'yellow')}
- Updates may break compatibility with your current setup
- Always test thoroughly after updating
- Backups are created automatically before updates
- Your rive-config.js ensures local WASM files are used
`);
}

/**
 * Interactive mode
 */
function interactiveMode() {
    console.log(colorize('🎯 Rive Update Tool - Interactive Mode', 'bright'));
    
    const hasChanges = compareVersions();
    
    if (!hasChanges) {
        console.log(colorize('\n✅ No updates needed - files are identical', 'green'));
        return;
    }

    console.log(colorize('\n❓ Would you like to update Rive files?', 'yellow'));
    console.log('This will:');
    console.log('1. Create a backup of current files');
    console.log('2. Copy new files from NPM package');
    console.log('3. Preserve your rive-config.js settings');
    
    // In a real interactive scenario, you'd use readline
    // For now, we'll just show what would happen
    console.log(colorize('\n💡 To proceed, run: npm run update-rive -- --update', 'cyan'));
}

/**
 * Main function
 */
function main() {
    const args = process.argv.slice(2);

    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        return;
    }

    if (args.includes('--list-backups')) {
        listBackups();
        return;
    }

    if (args.includes('--compare') || args.includes('-c')) {
        compareVersions();
        return;
    }

    if (args.includes('--update') || args.includes('-u')) {
        const force = args.includes('--force');
        
        if (!force) {
            console.log(colorize('⚠️  This will update your Rive files and may break compatibility.', 'yellow'));
            console.log('Add --force to skip this warning.');
            return;
        }

        const hasChanges = compareVersions();
        if (hasChanges) {
            const backupPath = createBackup();
            updateRiveFiles();
            console.log(colorize(`\n💡 Backup created at: ${backupPath}`, 'blue'));
            console.log(colorize('🧪 Please test your application thoroughly!', 'yellow'));
        }
        return;
    }

    // Default: interactive mode
    interactiveMode();
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    compareVersions,
    createBackup,
    updateRiveFiles,
    listBackups
};
