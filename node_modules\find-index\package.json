{"name": "find-index", "version": "0.1.1", "description": "finds an item in an array matching a predicate function, and returns its index", "main": "index.js", "keywords": ["array", "findindex"], "files": ["index.js", "last.js"], "scripts": {"test": "node test/test"}, "homepage": "https://github.com/jsdf/find-index", "bugs": "https://github.com/jsdf/find-index/issues", "repository": {"type": "git", "url": "git://github.com/jsdf/find-index.git"}, "author": "<PERSON> <<EMAIL>> (http://jsdf.co/)", "license": "MIT"}