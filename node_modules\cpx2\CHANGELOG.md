# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

## [v7.0.2](https://github.com/bcomnes/cpx2/compare/v7.0.1...v7.0.2)

### Merged

- Upgrade: Bump glob from 10.4.4 to 11.0.0 [`#119`](https://github.com/bcomnes/cpx2/pull/119)
- Upgrade: Bump minimatch from 9.0.5 to 10.0.1 [`#118`](https://github.com/bcomnes/cpx2/pull/118)
- Upgrade: Bump nyc from 15.1.0 to 17.0.0 [`#117`](https://github.com/bcomnes/cpx2/pull/117)

### Fixed

- Set allowRelativePaths for apply acitons [`#113`](https://github.com/bcomnes/cpx2/issues/113)

### Commits

- Merge pull request #124 from bcomnes/fix-relative-copy-errors [`e35a68a`](https://github.com/bcomnes/cpx2/commit/e35a68ad3c268a6a8ea1ae2e3c17c89264251844)
- Merge pull request #123 from bcomnes/neostandard [`ab10190`](https://github.com/bcomnes/cpx2/commit/ab10190b16e7c00c212812b3d7b1ada514274359)
- Autofix [`1396a28`](https://github.com/bcomnes/cpx2/commit/1396a28cf26903aa1f4796c3f9d902a3dff32241)

## [v7.0.1](https://github.com/bcomnes/cpx2/compare/v7.0.0...v7.0.1) - 2023-12-29

### Commits

- fix: minimum node version down to 18 [`05a55cc`](https://github.com/bcomnes/cpx2/commit/05a55cc2907b49197a64bc576ee15ea83a76ff92)

## [v7.0.0](https://github.com/bcomnes/cpx2/compare/v6.0.1...v7.0.0) - 2023-12-25

### Commits

- Remove files filter [`e9ef541`](https://github.com/bcomnes/cpx2/commit/e9ef541d5c2e0ca8fb5614a470def89443515532)
- Bump minimum node version to lts 20 [`56bbc19`](https://github.com/bcomnes/cpx2/commit/56bbc19f9d5f50037ad222792d323920677836e9)
- Upgrade: Bump debounce from 1.2.1 to 2.0.0 [`390cd9d`](https://github.com/bcomnes/cpx2/commit/390cd9dda1a57d6d775d1b93bd379ffc4c1f9b43)

## [v6.0.1](https://github.com/bcomnes/cpx2/compare/v6.0.0...v6.0.1) - 2023-10-25

### Merged

- Upgrade: Bump actions/setup-node from 3 to 4 [`#111`](https://github.com/bcomnes/cpx2/pull/111)

### Commits

- Fix windows bugs with new glob [`eb7ab6a`](https://github.com/bcomnes/cpx2/commit/eb7ab6a881cd1259fa2d43423837b4428747f397)
- Add more OSs to tests [`35128d8`](https://github.com/bcomnes/cpx2/commit/35128d81b7e625ddfd73fbd2695e1ea01fab53c5)

## [v6.0.0](https://github.com/bcomnes/cpx2/compare/v5.0.0...v6.0.0) - 2023-10-22

### Merged

- Update to the latest glob [`#110`](https://github.com/bcomnes/cpx2/pull/110)
- Upgrade: Bump actions/checkout from 3 to 4 [`#109`](https://github.com/bcomnes/cpx2/pull/109)

### Commits

- Update test.yml [`f93bad5`](https://github.com/bcomnes/cpx2/commit/f93bad5f774f71185e97286b697ef88815d64433)
- Fix import [`f03ed57`](https://github.com/bcomnes/cpx2/commit/f03ed5740ca0eb52a1ad409a09ecfc965f7651d1)
- Fix CI versions [`65f2e64`](https://github.com/bcomnes/cpx2/commit/65f2e64595f8c8162198c05cda9901f66ab79acd)

## [v5.0.0](https://github.com/bcomnes/cpx2/compare/v4.2.3...v5.0.0) - 2023-07-05

### Merged

- Upgrade: Bump minimatch from 8.0.4 to 9.0.0 [`#107`](https://github.com/bcomnes/cpx2/pull/107)

### Commits

- Update deps and require &gt;= node 16 [`1862ff6`](https://github.com/bcomnes/cpx2/commit/1862ff62d0d1db57f1230a4df84b9ccd1e8a63be)
- Fix imports [`e1b5778`](https://github.com/bcomnes/cpx2/commit/e1b577837014de19a613a02ea438f53d3b76892e)

## [v4.2.3](https://github.com/bcomnes/cpx2/compare/v4.2.2...v4.2.3) - 2023-04-03

### Merged

- Upgrade: Bump minimatch from 7.4.4 to 8.0.2 [`#106`](https://github.com/bcomnes/cpx2/pull/106)

## [v4.2.2](https://github.com/bcomnes/cpx2/compare/v4.2.1...v4.2.2) - 2023-03-07

### Merged

- Upgrade: Bump mocha from 9.2.2 to 10.2.0 [`#79`](https://github.com/bcomnes/cpx2/pull/79)

## [v4.2.1](https://github.com/bcomnes/cpx2/compare/v4.2.0...v4.2.1) - 2023-03-07

### Merged

- Deps [`#105`](https://github.com/bcomnes/cpx2/pull/105)

### Commits

- Update actions [`ffb450b`](https://github.com/bcomnes/cpx2/commit/ffb450b88d71b7f2bab1a935c6725814b0f076c5)
- Remove rimraf [`45c5991`](https://github.com/bcomnes/cpx2/commit/45c59919a8af37f37aa54f56326480de5c6c441e)
- Update shell quote [`4f20279`](https://github.com/bcomnes/cpx2/commit/4f20279e5cf9010d661119e5f86cfa196efb8fcd)

## [v4.2.0](https://github.com/bcomnes/cpx2/compare/v4.1.2...v4.2.0) - 2022-02-08

### Merged

- Fix #53: Overwrite readonly [`#57`](https://github.com/bcomnes/cpx2/pull/57)
- Upgrade: Bump actions/setup-node from 2.5.0 to 2.5.1 [`#52`](https://github.com/bcomnes/cpx2/pull/52)

### Fixed

- Merge pull request #57 from schutm/overwrite-readonly [`#53`](https://github.com/bcomnes/cpx2/issues/53)

### Commits

- Add force flag to overwrite readonly files [`e622923`](https://github.com/bcomnes/cpx2/commit/e622923a4ab495571482958d77ddd0aa7a9a5243)
- Eat exception if target doesn't exist [`f7c1fb3`](https://github.com/bcomnes/cpx2/commit/f7c1fb3ddbd3a57b4f18c4b117dc5f9bd28eeea1)
- Pin to major actions version [`94a5f34`](https://github.com/bcomnes/cpx2/commit/94a5f347bbb5d0e53a2d8156b390f634e5a543ae)

## [v4.1.2](https://github.com/bcomnes/cpx2/compare/v4.1.1...v4.1.2) - 2021-12-23

### Merged

- Allow watching from a relative directory again [`#51`](https://github.com/bcomnes/cpx2/pull/51)

### Fixed

- Allow watching from a relative directory again [`#44`](https://github.com/bcomnes/cpx2/issues/44)

## [v4.1.1](https://github.com/bcomnes/cpx2/compare/v4.1.0...v4.1.1) - 2021-12-22

### Merged

- Remove co [`#50`](https://github.com/bcomnes/cpx2/pull/50)

### Commits

- Remove co from watch tests [`f296a6b`](https://github.com/bcomnes/cpx2/commit/f296a6baa040868c2cb59b09aa520cd6a6bc8ada)
- Remove co in watcher [`7191ee9`](https://github.com/bcomnes/cpx2/commit/7191ee967dd760ac64d629e12bb561d8fd6ff3e0)
- Remove co from copy tests [`1f72f04`](https://github.com/bcomnes/cpx2/commit/1f72f04e9845f8957a5011ef83a5b0bdbdc51443)

## [v4.1.0](https://github.com/bcomnes/cpx2/compare/v4.0.0...v4.1.0) - 2021-12-22

### Merged

- Upgrade: Bump actions/checkout from 2.3.4 to 2.4.0 [`#39`](https://github.com/bcomnes/cpx2/pull/39)
- Upgrade: Bump actions/setup-node from 2.4.0 to 2.5.0 [`#45`](https://github.com/bcomnes/cpx2/pull/45)
- Return a report object containing details about the copy operation [`#49`](https://github.com/bcomnes/cpx2/pull/49)

## [v4.0.0](https://github.com/bcomnes/cpx2/compare/v3.0.2...v4.0.0) - 2021-09-15

### Merged

- Add gitignore style ignore support  [`#32`](https://github.com/bcomnes/cpx2/pull/32)

### Commits

- **Breaking change:** Require node &gt;=14 [`074e5b5`](https://github.com/bcomnes/cpx2/commit/074e5b5d338cf57df2cb0e02ec654e3b5c3e080f)
- Add ignore option and flag [`be9680c`](https://github.com/bcomnes/cpx2/commit/be9680c7e8e84ebb1b43c089d53d9b6e3bbf4a4b)

## [v3.0.2](https://github.com/bcomnes/cpx2/compare/v3.0.1...v3.0.2) - 2021-08-13

### Commits

- Revert "Merge pull request #23 from mhanberg/mh/exit-on-stdin-closing" [`d1275a0`](https://github.com/bcomnes/cpx2/commit/d1275a0f6d26e3084deaa3b4d20b7d8524c6719d)

## [v3.0.1](https://github.com/bcomnes/cpx2/compare/v3.0.0...v3.0.1) - 2021-08-09

### Merged

- Upgrade: Bump actions/setup-node from 2.3.2 to 2.4.0 [`#31`](https://github.com/bcomnes/cpx2/pull/31)
- Exit when stdin closes [`#23`](https://github.com/bcomnes/cpx2/pull/23)
- Upgrade: Bump fs-extra from 9.1.0 to 10.0.0 [`#17`](https://github.com/bcomnes/cpx2/pull/17)
- Upgrade: Bump gh-release from 4.0.4 to 6.0.0 [`#20`](https://github.com/bcomnes/cpx2/pull/20)
- Upgrade: Bump mocha from 8.4.0 to 9.0.3 [`#28`](https://github.com/bcomnes/cpx2/pull/28)
- Upgrade: Bump actions/setup-node from 2.1.2 to 2.3.2 [`#30`](https://github.com/bcomnes/cpx2/pull/30)
- Upgrade: Bump actions/checkout from v2.3.3 to v2.3.4 [`#11`](https://github.com/bcomnes/cpx2/pull/11)
- Upgrade: Bump bcomnes/npm-bump from v2.0.1 to v2.0.2 [`#10`](https://github.com/bcomnes/cpx2/pull/10)
- Upgrade: Bump actions/setup-node from v2.1.1 to v2.1.2 [`#9`](https://github.com/bcomnes/cpx2/pull/9)
- Upgrade: Bump actions/checkout from v2.3.2 to v2.3.3 [`#8`](https://github.com/bcomnes/cpx2/pull/8)

### Commits

- Lint [`9db062f`](https://github.com/bcomnes/cpx2/commit/9db062f4e5131540b3a39ae58860a49b2fdf191a)

## [v3.0.0](https://github.com/bcomnes/cpx2/compare/v2.0.0...v3.0.0) - 2020-09-18

### Merged

- Upgrade: Bump @mysticatea/eslint-plugin from 11.0.0 to 13.0.0 [`#4`](https://github.com/bcomnes/cpx2/pull/4)
- Upgrade: Bump nyc from 14.1.1 to 15.1.0 [`#5`](https://github.com/bcomnes/cpx2/pull/5)
- Upgrade: Bump eslint from 6.8.0 to 7.9.0 [`#6`](https://github.com/bcomnes/cpx2/pull/6)
- Upgrade: Bump fs-extra from 8.1.0 to 9.0.1 [`#7`](https://github.com/bcomnes/cpx2/pull/7)
- Upgrade: Bump mocha from 6.2.3 to 8.1.3 [`#3`](https://github.com/bcomnes/cpx2/pull/3)
- try building windows [`#2`](https://github.com/bcomnes/cpx2/pull/2)

### Commits

- switch to github actions and dependabot [`9eaf5e0`](https://github.com/bcomnes/cpx2/commit/9eaf5e0e37d57e197cdc607a6c79148c4d9428a6)
- Add release action [`399f37f`](https://github.com/bcomnes/cpx2/commit/399f37f8b06f66623278d24713f28a321bdf4ee4)
- Fix lint errors [`8e96f93`](https://github.com/bcomnes/cpx2/commit/8e96f93631b250121ec4c570474d96a3d362d542)

## [v2.0.0](https://github.com/bcomnes/cpx2/compare/v1.5.0...v2.0.0) - 2019-08-26

### Merged

- Maintenance tasks  [`#1`](https://github.com/bcomnes/cpx2/pull/1)
- Fix: fix vulnerability [`#43`](https://github.com/bcomnes/cpx2/pull/43)
- Docs: fix typo w/ watch-ready [`#31`](https://github.com/bcomnes/cpx2/pull/31)
- Add "text-summary" nyc reporter [`#26`](https://github.com/bcomnes/cpx2/pull/26)
- Fix typo "untachable" =&gt; "untouchable" [`#25`](https://github.com/bcomnes/cpx2/pull/25)

### Commits

- Chore: remove package-lock.json [`db3e7f0`](https://github.com/bcomnes/cpx2/commit/db3e7f0c2c2d153cb6223f92afd744a7d3bd7d30)
- Breaking: stop transpiling [`e1d9b6e`](https://github.com/bcomnes/cpx2/commit/e1d9b6ed975d4229966560c35658a1ca5667d5e1)
- Rewrite implementation [`3098ae3`](https://github.com/bcomnes/cpx2/commit/3098ae350f8069e73d11bf6e787aaee833643514)

## [v1.5.0](https://github.com/bcomnes/cpx2/compare/v1.4.0...v1.5.0) - 2016-09-05

### Commits

- New: `--include-empty-dirs` option [`6324e50`](https://github.com/bcomnes/cpx2/commit/6324e50df948cffd9521838e437d6435e01e410f)
- New: `--no-initial` option [`bae874a`](https://github.com/bcomnes/cpx2/commit/bae874a1b0237a431fc2d8f25c4e4b58e7572ac4)
- Docs: fix wrong link of Changelog. [`77b1627`](https://github.com/bcomnes/cpx2/commit/77b1627bdd2a1bf6dfa2b593def6b9dec3d3390b)

## [v1.4.0](https://github.com/bcomnes/cpx2/compare/v1.3.2...v1.4.0) - 2016-09-03

### Merged

- Revise README.md [`#20`](https://github.com/bcomnes/cpx2/pull/20)

### Fixed

- New: `--update` option to disallow overwriting (fixes #12) [`#12`](https://github.com/bcomnes/cpx2/issues/12)

### Commits

- New: `--preserve` option to copy uid,gid,atime,mtime. [`217004b`](https://github.com/bcomnes/cpx2/commit/217004b7c346ce046a27d11a0edfa5950ee98000)
- Chore: add some tests for misc. [`82be9ae`](https://github.com/bcomnes/cpx2/commit/82be9aeb883db105a5be0992568ffb4902545dd7)
- Docs: add changelog/contributing sections [`3de04d5`](https://github.com/bcomnes/cpx2/commit/3de04d5bf35d435529e22051e2e78db767bfde27)

## [v1.3.2](https://github.com/bcomnes/cpx2/compare/v1.3.1...v1.3.2) - 2016-07-20

### Fixed

- Fix: check whether 'destroy' method exists or not to transform (fixes #18) [`#18`](https://github.com/bcomnes/cpx2/issues/18)

### Commits

- Chore: upgrade ESLint and my eslintrc [`2a68c2f`](https://github.com/bcomnes/cpx2/commit/2a68c2fbc9203bb234d906c3eb99a796f144b0dd)
- Chore: upgrade dependencies. [`07e3dc2`](https://github.com/bcomnes/cpx2/commit/07e3dc2db3cafb52e30990b96dc23c570fb28bdd)
- Chore: executes eslint only if node version &gt;=4 [`11297e5`](https://github.com/bcomnes/cpx2/commit/11297e525dc5e9051dcde379a67b3595f81523da)

## [v1.3.1](https://github.com/bcomnes/cpx2/compare/v1.3.0...v1.3.1) - 2016-03-11

### Commits

- Fix; add bin [`36f9212`](https://github.com/bcomnes/cpx2/commit/36f9212f43106fe97e96c7a68717189c7f837122)

## [v1.3.0](https://github.com/bcomnes/cpx2/compare/v1.2.1...v1.3.0) - 2016-03-10

### Fixed

- New: `--dereference` option (fixes #14) [`#14`](https://github.com/bcomnes/cpx2/issues/14)
- Fix: failed initial copies if target path starts with './' (fixes #13) [`#13`](https://github.com/bcomnes/cpx2/issues/13)

### Commits

- Upgrade: dependencies. [`e5e4722`](https://github.com/bcomnes/cpx2/commit/e5e4722941614a53e28be410cd64dc48b3ad4314)
- Docs: Update README.md [`25da2f7`](https://github.com/bcomnes/cpx2/commit/25da2f7084db60b88c22e4f77dc85ae3fd58a9c1)

## [v1.2.1](https://github.com/bcomnes/cpx2/compare/v1.2.0...v1.2.1) - 2015-07-19

### Commits

- Fix: copying of a file in cwd (refs #11) [`b150cdd`](https://github.com/bcomnes/cpx2/commit/b150cddffa061a3c6f5011d63df821c1e1d7d724)
- Update dependencies. [`861c36c`](https://github.com/bcomnes/cpx2/commit/861c36c6299f458e599538c00783baa35606fd95)

## [v1.2.0](https://github.com/bcomnes/cpx2/compare/v1.1.6...v1.2.0) - 2015-05-17

### Commits

- Update dependencies. [`476d8a1`](https://github.com/bcomnes/cpx2/commit/476d8a1cffdd0bc4a1f4cb85a16e5abf77a37b3e)
- working on node 0.10 support [`c43544d`](https://github.com/bcomnes/cpx2/commit/c43544dde1293b37746a094dbf1d495a402cd3a4)
- Update dependencies. [`b2c5ec5`](https://github.com/bcomnes/cpx2/commit/b2c5ec56c4649445f9f8e9545abccfd8074df156)

## [v1.1.6](https://github.com/bcomnes/cpx2/compare/v1.1.5...v1.1.6) - 2015-04-19

### Commits

- Update dependencies. [`70e931a`](https://github.com/bcomnes/cpx2/commit/70e931af14d12d0b54959c802e2ee3fd7c55bd06)
- Update dependencies. [`8c12bae`](https://github.com/bcomnes/cpx2/commit/8c12baef3896357af6c0f76e5e52290065a23e84)
- Trivial fix. [`b20c35d`](https://github.com/bcomnes/cpx2/commit/b20c35d0510475ca4447ec9052ea6ae799b19c3f)

## [v1.1.5](https://github.com/bcomnes/cpx2/compare/v1.1.4...v1.1.5) - 2015-04-05

### Commits

- Update dependencies. [`617bf98`](https://github.com/bcomnes/cpx2/commit/617bf98505f2c75ff7a011f45b7fb956fdbc88af)
- trivial update [`ee1a646`](https://github.com/bcomnes/cpx2/commit/ee1a6460139692c68a427aba901e2b32b9983b66)
- Update README.md [`a15b87d`](https://github.com/bcomnes/cpx2/commit/a15b87d9fc67a990d337f50f18fbf9b7cb24b4d3)

## [v1.1.4](https://github.com/bcomnes/cpx2/compare/v1.1.3...v1.1.4) - 2015-03-22

### Commits

- Update README.md [`5e6614c`](https://github.com/bcomnes/cpx2/commit/5e6614c5cef1605e5b9a5d6c9d985462dde8e187)
- Fix tests for linux [`732c766`](https://github.com/bcomnes/cpx2/commit/732c766fb33cf7ac82d5cfc340aa95007239bdaa)
- Update README.md [`ab1559c`](https://github.com/bcomnes/cpx2/commit/ab1559ce1084ed663bf381af1c5383f9d65ee00e)

## [v1.1.3](https://github.com/bcomnes/cpx2/compare/v1.1.2...v1.1.3) - 2015-03-18

### Commits

- Remove warning. [`eab1417`](https://github.com/bcomnes/cpx2/commit/eab14174a6daf0a546cd3f9b07aa7a5ea251bdf4)

## [v1.1.2](https://github.com/bcomnes/cpx2/compare/v1.1.1...v1.1.2) - 2015-03-18

### Commits

- Update dependencies [`6042f55`](https://github.com/bcomnes/cpx2/commit/6042f5521261271a1c2327d8124cbe9b1fb9fcac)
- Use run-all [`b58107b`](https://github.com/bcomnes/cpx2/commit/b58107b9f3e6b5f7e6615450f4a96bbd564008e5)
- Fix lint errors. [`8c1a332`](https://github.com/bcomnes/cpx2/commit/8c1a3321ddd192d86c98a510c49667dd39906c8f)

## [v1.1.1](https://github.com/bcomnes/cpx2/compare/v1.1.0...v1.1.1) - 2015-03-14

### Commits

- shebang [`7b78017`](https://github.com/bcomnes/cpx2/commit/7b780171d79778a73b6ad6c8925c46b8efac7ef7)

## [v1.1.0](https://github.com/bcomnes/cpx2/compare/v1.0.0...v1.1.0) - 2015-03-14

### Fixed

- Add --transform option [`#1`](https://github.com/bcomnes/cpx2/issues/1)

### Commits

- Refactoring. [`6f7491d`](https://github.com/bcomnes/cpx2/commit/6f7491d9ef5363177cda885dc64d24afbdbf97cd)
- Add --command option [`ff79af1`](https://github.com/bcomnes/cpx2/commit/ff79af15c065f3f297c570363a8e85924c0598d3)
- Switch argv parser to subarg. [`51a31b0`](https://github.com/bcomnes/cpx2/commit/51a31b07ea740cff63a14844883394dcfb220847)

## v1.0.0 - 2015-03-09

### Commits

- Add the first version. [`61586d4`](https://github.com/bcomnes/cpx2/commit/61586d498ef888f7010daa417c0f6487da8a9561)
- Add README.md [`84ff5de`](https://github.com/bcomnes/cpx2/commit/84ff5dee40851db47b057c647f9c77c6f412bae9)
- Initial commit [`7ba3043`](https://github.com/bcomnes/cpx2/commit/7ba30435d6e918dfcf0c179c14717ab1aaf25ad9)
